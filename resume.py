from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.lib.units import inch

# === File name ===
file_path = "Basant_Kumar_Digal_Resume.pdf"

# === Styles ===
styles = getSampleStyleSheet()
styles.add(ParagraphStyle(name='HeaderName', fontSize=20, leading=24, alignment=1,
                          fontName="Helvetica-Bold", textColor=colors.HexColor("#1F618D")))
styles.add(ParagraphStyle(name='SectionHeading', fontSize=12, leading=14, spaceAfter=6,
                          fontName="Helvetica-Bold", textColor=colors.HexColor("#1F618D")))
styles.add(ParagraphStyle(name='BodyText', fontSize=10, leading=14, textColor=colors.black))

# === Document Setup ===
doc = SimpleDocTemplate(file_path, pagesize=A4,
                        rightMargin=30, leftMargin=30,
                        topMargin=30, bottomMargin=30)

content = []

# === Header (Full width) ===
content.append(Paragraph("BASANT KUMAR DIGAL", styles['HeaderName']))
content.append(Paragraph("Hospital Administration Professional | 15+ Years in Patient Care & Front Office Operations",
                         styles['BodyText']))
content.append(Spacer(1, 12))

# === Two Column Layout ===
# Left column = sidebar | Right column = main content
sidebar = []
main = []

# --- Sidebar (Light Gray Background) ---
sidebar.append(Paragraph("<b>📍 Contact</b>", styles['SectionHeading']))
sidebar.append(Paragraph("Bhubaneswar, Odisha<br/>📞 +91 **********<br/>✉️ <EMAIL>",
                         styles['BodyText']))
sidebar.append(Spacer(1, 12))

sidebar.append(Paragraph("<b>⚡ Core Skills</b>", styles['SectionHeading']))
sidebar.append(Paragraph("• Hospital Administration<br/>• Patient Relations<br/>"
                         "• Billing & TPA Liaison<br/>• Team Leadership<br/>"
                         "• Complaint Handling<br/>• Record Management",
                         styles['BodyText']))
sidebar.append(Spacer(1, 12))

sidebar.append(Paragraph("<b>🌐 Languages</b>", styles['SectionHeading']))
sidebar.append(Paragraph("Odia | Hindi | English", styles['BodyText']))
sidebar.append(Spacer(1, 12))

sidebar.append(Paragraph("<b>🎯 Interests</b>", styles['SectionHeading']))
sidebar.append(Paragraph("Traveling | Hiking | Bike Riding<br/>Cricket & Football",
                         styles['BodyText']))

# --- Main Section ---
main.append(Paragraph("💼 <b>Professional Summary</b>", styles['SectionHeading']))
main.append(Paragraph(
    "Dedicated and results-driven hospital administration professional with over 15 years "
    "of experience in front office management, patient relations, and ward operations. Proven "
    "ability to enhance patient care workflows, resolve billing concerns, and ensure seamless "
    "coordination between patients, doctors, and hospital staff. Skilled in training and mentoring "
    "teams, improving operational efficiency, and maintaining compliance with hospital policies.",
    styles['BodyText']))
main.append(Spacer(1, 12))

main.append(Paragraph("🏢 <b>Professional Experience</b>", styles['SectionHeading']))
main.append(Paragraph("<b>Apollo Hospitals, Bhubaneswar, Odisha</b><br/>"
                      "Supervisor (Ward Secretary) | 2010 – Present<br/>"
                      "- Supervised ward secretaries, ensuring adherence to hospital policies.<br/>"
                      "- Coordinated admissions, discharges, and transfers efficiently.<br/>"
                      "- Handled patient complaints, billing concerns, and family queries.<br/>"
                      "- Maintained accurate patient records and management reports.<br/>"
                      "- Mentored and trained staff, improving service quality.",
                      styles['BodyText']))
main.append(Spacer(1, 8))

main.append(Paragraph("<b>Hemalata Hospital, Bhubaneswar, Odisha</b><br/>"
                      "Front Office Executive | 2008 – 2010<br/>"
                      "- Managed front office operations, ensuring a welcoming environment.<br/>"
                      "- Handled patient inquiries, discharge processes, and billing issues.<br/>"
                      "- Coordinated meetings between relatives and doctors.<br/>"
                      "- Liaised with TPAs and corporate clients for billing reimbursements.",
                      styles['BodyText']))
main.append(Spacer(1, 12))

main.append(Paragraph("🎓 <b>Education</b>", styles['SectionHeading']))
main.append(Paragraph("B.A. – Utkal University, 2008<br/>"
                      "Intermediate – Biju Patnaik College Of Science & Education, 2004<br/>"
                      "Matriculation – IRC Village Govt. High School, 2001",
                      styles['BodyText']))
main.append(Spacer(1, 12))

main.append(Paragraph("📜 <b>Certifications</b>", styles['SectionHeading']))
main.append(Paragraph("Hospitality Service – ILEAD, 2007<br/>"
                      "PD DCA (Professional Diploma in Computer Applications) – ILEAD, 2007",
                      styles['BodyText']))
main.append(Spacer(1, 12))

main.append(Paragraph("🖋 <b>Declaration</b>", styles['SectionHeading']))
main.append(Paragraph(
    "I hereby declare that all the information provided above is true and correct to the best "
    "of my knowledge and belief.",
    styles['BodyText']))
main.append(Spacer(1, 20))
main.append(Paragraph("Place: Bhubaneswar<br/>Date: ___________<br/><br/>(Basant Kumar Digal)",
                      styles['BodyText']))

# === Combine sidebar + main into two columns ===
table_data = [[sidebar, main]]
table = Table(table_data, colWidths=[180, 350])

table.setStyle(TableStyle([
    ('BACKGROUND', (0, 0), (0, 0), colors.HexColor("#F4F6F7")),  # Sidebar bg
    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ('INNERGRID', (0, 0), (-1, -1), colors.white, 0),  # No inner borders
    ('BOX', (0, 0), (-1, -1), colors.white, 0),
]))

content.append(table)

# === Build PDF ===
doc.build(content)

print(f"✅ Resume created: {file_path}")
